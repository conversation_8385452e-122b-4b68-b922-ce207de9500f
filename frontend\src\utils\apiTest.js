/**
 * API测试工具
 * 用于测试前端与后端API的连接
 */

import seatApi from "@/api/seat";
import userApi from "@/api/user";

export const testAPIs = async () => {
  const results = {
    rooms: { success: false, error: null, data: null },
    userInfo: { success: false, error: null, data: null },
  };

  // 测试获取自习室列表
  try {
    console.log("测试获取自习室列表...");
    const roomsResponse = await seatApi.getRooms();
    results.rooms.success = true;
    results.rooms.data = roomsResponse.data;
    console.log("✅ 自习室列表获取成功:", roomsResponse.data);
  } catch (error) {
    results.rooms.error = error.message;
    console.error("❌ 自习室列表获取失败:", error);
  }

  // 测试获取用户信息（需要登录）
  try {
    console.log("测试获取用户信息...");
    const userResponse = await userApi.getUserInfo();
    results.userInfo.success = true;
    results.userInfo.data = userResponse.data;
    console.log("✅ 用户信息获取成功:", userResponse.data);
  } catch (error) {
    results.userInfo.error = error.message;
    console.log("⚠️ 用户信息获取失败（可能未登录）:", error.message);
  }

  return results;
};

export const testRoomAPI = async () => {
  try {
    console.log("测试自习室API...");
    const response = await seatApi.getRooms();
    console.log("自习室数据:", response.data);
    return response.data;
  } catch (error) {
    console.error("自习室API测试失败:", error);
    throw error;
  }
};

export const testSeatAPI = async (roomId = 1) => {
  try {
    console.log(`测试座位API (房间ID: ${roomId})...`);
    const today = new Date().toISOString().split("T")[0];
    const response = await seatApi.getSeatsByRoom(roomId, today);
    console.log("座位数据:", response.data);
    return response.data;
  } catch (error) {
    console.error("座位API测试失败:", error);
    throw error;
  }
};
