<template>
  <div class="websocket-test-container">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <h2>WebSocket连接测试</h2>
        </div>
      </template>

      <div class="test-content">
        <!-- 连接状态 -->
        <el-row :gutter="20" class="status-row">
          <el-col :span="12">
            <el-card class="status-card">
              <h3>座位状态连接</h3>
              <el-tag :type="seatStatusConnectionType">
                {{ seatStatusConnectionText }}
              </el-tag>
              <div class="connection-actions">
                <el-button
                  v-if="!seatStatusConnected"
                  type="primary"
                  @click="connectSeatStatus"
                  :loading="connecting"
                >
                  连接
                </el-button>
                <el-button v-else type="danger" @click="disconnectSeatStatus">
                  断开
                </el-button>
              </div>
            </el-card>
          </el-col>

          <el-col :span="12">
            <el-card class="status-card">
              <h3>通知连接</h3>
              <el-tag :type="notificationConnectionType">
                {{ notificationConnectionText }}
              </el-tag>
              <div class="connection-actions">
                <el-button
                  v-if="!notificationConnected"
                  type="primary"
                  @click="connectNotification"
                  :loading="connecting"
                >
                  连接
                </el-button>
                <el-button v-else type="danger" @click="disconnectNotification">
                  断开
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 测试操作 -->
        <el-card class="test-actions-card">
          <h3>测试操作</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-input
                v-model="testRoomId"
                placeholder="输入自习室ID"
                type="number"
              />
            </el-col>
            <el-col :span="8">
              <el-button
                type="primary"
                @click="subscribeSeatStatus"
                :disabled="!seatStatusConnected"
              >
                订阅座位状态
              </el-button>
            </el-col>
            <el-col :span="8">
              <el-button
                type="warning"
                @click="sendTestMessage"
                :disabled="!seatStatusConnected"
              >
                发送测试消息
              </el-button>
            </el-col>
          </el-row>
        </el-card>

        <!-- 消息日志 -->
        <el-card class="message-log-card">
          <template #header>
            <div class="card-header">
              <h3>消息日志</h3>
              <el-button type="info" @click="clearMessages">清空</el-button>
            </div>
          </template>

          <div class="message-log">
            <el-scrollbar height="300px">
              <div
                v-for="(message, index) in messages"
                :key="index"
                class="message-item"
                :class="message.type"
              >
                <div class="message-time">{{ message.time }}</div>
                <div class="message-content">{{ message.content }}</div>
              </div>
            </el-scrollbar>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { ElMessage } from "element-plus";
import wsManager from "@/utils/websocket";

export default {
  name: "WebSocketTest",
  setup() {
    // 响应式数据
    const seatStatusConnected = ref(false);
    const notificationConnected = ref(false);
    const connecting = ref(false);
    const testRoomId = ref(1);
    const messages = ref([]);

    // 计算属性
    const seatStatusConnectionType = computed(() => {
      return seatStatusConnected.value ? "success" : "danger";
    });

    const seatStatusConnectionText = computed(() => {
      return seatStatusConnected.value ? "已连接" : "未连接";
    });

    const notificationConnectionType = computed(() => {
      return notificationConnected.value ? "success" : "danger";
    });

    const notificationConnectionText = computed(() => {
      return notificationConnected.value ? "已连接" : "未连接";
    });

    // 方法
    const addMessage = (content, type = "info") => {
      const now = new Date();
      const time = `${now.getHours().toString().padStart(2, "0")}:${now
        .getMinutes()
        .toString()
        .padStart(2, "0")}:${now.getSeconds().toString().padStart(2, "0")}`;

      messages.value.unshift({
        time,
        content,
        type,
      });

      // 限制消息数量
      if (messages.value.length > 100) {
        messages.value = messages.value.slice(0, 100);
      }
    };

    const connectSeatStatus = () => {
      connecting.value = true;
      addMessage("正在连接座位状态WebSocket...", "info");

      const wsUrl = `ws://localhost:8000/ws/seat/${testRoomId.value}/`;

      wsManager.connect(wsUrl, "seatStatusTest", {
        onOpen: () => {
          addMessage("座位状态WebSocket连接已建立", "success");
        },
        onAuthenticated: (data) => {
          seatStatusConnected.value = true;
          connecting.value = false;
          addMessage(`座位状态WebSocket认证成功: ${data.message}`, "success");

          // 自动订阅座位状态
          subscribeSeatStatus();
        },
        onMessage: (data) => {
          addMessage(`收到消息: ${JSON.stringify(data)}`, "info");
        },
        onClose: (event) => {
          seatStatusConnected.value = false;
          connecting.value = false;
          addMessage(`座位状态WebSocket连接已关闭: ${event.code}`, "warning");
        },
        onError: (error) => {
          connecting.value = false;
          addMessage(`座位状态WebSocket连接错误: ${error}`, "error");
          ElMessage.error("WebSocket连接失败");
        },
      });
    };

    const disconnectSeatStatus = () => {
      wsManager.disconnect("seatStatusTest");
      seatStatusConnected.value = false;
      addMessage("座位状态WebSocket连接已断开", "warning");
    };

    const connectNotification = () => {
      connecting.value = true;
      addMessage("正在连接通知WebSocket...", "info");

      const wsUrl = "ws://localhost:8000/ws/notifications/";

      wsManager.connect(wsUrl, "notificationTest", {
        onOpen: () => {
          addMessage("通知WebSocket连接已建立", "success");
        },
        onAuthenticated: (data) => {
          notificationConnected.value = true;
          connecting.value = false;
          addMessage(`通知WebSocket认证成功: ${data.message}`, "success");
        },
        onMessage: (data) => {
          addMessage(`收到通知: ${JSON.stringify(data)}`, "info");
        },
        onClose: (event) => {
          notificationConnected.value = false;
          connecting.value = false;
          addMessage(`通知WebSocket连接已关闭: ${event.code}`, "warning");
        },
        onError: (error) => {
          connecting.value = false;
          addMessage(`通知WebSocket连接错误: ${error}`, "error");
          ElMessage.error("WebSocket连接失败");
        },
      });
    };

    const disconnectNotification = () => {
      wsManager.disconnect("notificationTest");
      notificationConnected.value = false;
      addMessage("通知WebSocket连接已断开", "warning");
    };

    const subscribeSeatStatus = () => {
      if (!seatStatusConnected.value) {
        ElMessage.warning("请先连接座位状态WebSocket");
        return;
      }

      wsManager.subscribeSeatStatus("seatStatusTest", testRoomId.value);
      addMessage(`已订阅自习室 ${testRoomId.value} 的座位状态`, "success");
    };

    const sendTestMessage = () => {
      if (!seatStatusConnected.value) {
        ElMessage.warning("请先连接座位状态WebSocket");
        return;
      }

      const testMessage = {
        type: "test",
        message: "这是一条测试消息",
        timestamp: Date.now(),
      };

      wsManager.send("seatStatusTest", testMessage);
      addMessage(`发送测试消息: ${JSON.stringify(testMessage)}`, "info");
    };

    const clearMessages = () => {
      messages.value = [];
      addMessage("消息日志已清空", "info");
    };

    // 生命周期
    onMounted(() => {
      addMessage("WebSocket测试页面已加载", "info");
    });

    onUnmounted(() => {
      // 清理WebSocket连接
      wsManager.disconnect("seatStatusTest");
      wsManager.disconnect("notificationTest");
    });

    return {
      seatStatusConnected,
      notificationConnected,
      connecting,
      testRoomId,
      messages,
      seatStatusConnectionType,
      seatStatusConnectionText,
      notificationConnectionType,
      notificationConnectionText,
      connectSeatStatus,
      disconnectSeatStatus,
      connectNotification,
      disconnectNotification,
      subscribeSeatStatus,
      sendTestMessage,
      clearMessages,
    };
  },
};
</script>

<style scoped>
.websocket-test-container {
  padding: 20px;
}

.test-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2,
.card-header h3 {
  margin: 0;
  color: #303133;
}

.test-content {
  padding: 20px 0;
}

.status-row {
  margin-bottom: 20px;
}

.status-card {
  text-align: center;
  padding: 20px;
}

.status-card h3 {
  margin-bottom: 10px;
  color: #606266;
}

.connection-actions {
  margin-top: 15px;
}

.test-actions-card,
.message-log-card {
  margin-top: 20px;
}

.test-actions-card h3 {
  margin-bottom: 15px;
  color: #606266;
}

.message-log {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 10px;
}

.message-item {
  padding: 8px 12px;
  margin-bottom: 5px;
  border-radius: 4px;
  border-left: 4px solid #dcdfe6;
}

.message-item.success {
  background-color: #f0f9ff;
  border-left-color: #67c23a;
}

.message-item.error {
  background-color: #fef0f0;
  border-left-color: #f56c6c;
}

.message-item.warning {
  background-color: #fdf6ec;
  border-left-color: #e6a23c;
}

.message-item.info {
  background-color: #f4f4f5;
  border-left-color: #909399;
}

.message-time {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.message-content {
  font-size: 14px;
  color: #606266;
  word-break: break-all;
}
</style>
