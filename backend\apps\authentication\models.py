from django.db import models
from django.utils import timezone
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager, PermissionsMixin
import binascii
import secrets
from utils.crypto import SM4Crypto

class UserManager(BaseUserManager):
    """自定义用户管理器"""

    def create_user(self, student_id, password=None, **extra_fields):
        """
        创建普通用户
        """
        if not student_id:
            raise ValueError('用户必须有学号')

        from utils.crypto import SM3Hasher

        # 计算学号的SM3哈希值用于索引
        student_id_hash = SM3Hasher.hash(student_id)

        # 获取系统SM4密钥
        sm4_key = SystemConfig.get_sm4_key()

        # 加密学号
        encrypted_student_id = SM4Crypto.encrypt(sm4_key, student_id)
        student_id_binary = binascii.unhexlify(encrypted_student_id)

        # 创建用户
        user = self.model(
            student_id=student_id_binary,
            student_id_hash=student_id_hash,
            **extra_fields
        )

        # 设置密码
        if password:
            user.set_password(password)

        user.save(using=self._db)
        return user

    def create_superuser(self, student_id, password=None, **extra_fields):
        """
        创建超级用户
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('status', 'active')

        if extra_fields.get('is_staff') is not True:
            raise ValueError('超级用户必须有is_staff=True')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('超级用户必须有is_superuser=True')

        return self.create_user(student_id, password, **extra_fields)


class User(models.Model):
    """用户模型"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    student_id = models.BinaryField(max_length=64, verbose_name='SM4加密的学号')
    student_id_hash = models.CharField(max_length=64, unique=True, verbose_name='SM3哈希的学号(用于索引)')
    password = models.CharField(max_length=64, verbose_name='SM3哈希的密码')
    salt = models.CharField(max_length=32, null=True, blank=True, verbose_name='密码盐值')
    iterations = models.IntegerField(default=10000, verbose_name='密码哈希迭代次数')
    public_key = models.TextField(null=True, blank=True, verbose_name='SM2公钥')
    public_key_expires = models.DateTimeField(null=True, blank=True, verbose_name='公钥过期时间')
    email = models.BinaryField(max_length=128, null=True, blank=True, verbose_name='SM4加密的邮箱')
    phone = models.BinaryField(max_length=64, null=True, blank=True, verbose_name='SM4加密的手机号')
    status = models.CharField(max_length=20, default='active', verbose_name='状态(active/disabled/blacklisted/locked)')
    credit_score = models.IntegerField(default=100, verbose_name='信誉分')
    login_attempts = models.IntegerField(default=0, verbose_name='登录失败次数')
    last_login = models.DateTimeField(null=True, blank=True, verbose_name='最后登录时间')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    # Django admin兼容字段
    is_staff = models.BooleanField(default=False, verbose_name='是否为管理员')
    is_superuser = models.BooleanField(default=False, verbose_name='是否为超级用户')
    is_active = models.BooleanField(default=True, verbose_name='是否激活')

    objects = UserManager()

    USERNAME_FIELD = 'student_id_hash'
    REQUIRED_FIELDS = []

    class Meta:
        db_table = 'user'
        verbose_name = '用户'
        verbose_name_plural = '用户'
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['credit_score']),
        ]

    def __str__(self):
        return f"User {self.id} - {self.student_id_hash[:10]}..."

    def get_username(self):
        """
        返回用户的唯一标识符
        """
        return self.student_id_hash

    def set_password(self, raw_password):
        """
        设置用户密码
        """
        if not raw_password:
            self.password = None
            self.salt = None
            self.iterations = 10000
        else:
            from utils.crypto import SM3Hasher
            password_hash_result = SM3Hasher.hash_with_salt(raw_password)
            self.password = password_hash_result['hash']
            self.salt = password_hash_result['salt']
            self.iterations = password_hash_result['iterations']

    def check_password(self, raw_password):
        """
        检查密码是否正确
        """
        if not self.password or not raw_password:
            return False

        from utils.crypto import SM3Hasher
        return SM3Hasher.verify(raw_password, self.password, self.salt, self.iterations)

    def has_perm(self, perm, obj=None):
        """
        检查用户是否有指定权限
        """
        # 超级用户拥有所有权限
        if self.is_superuser:
            return True
        return False

    def has_module_perms(self, app_label):
        """
        检查用户是否有访问指定应用的权限
        """
        # 超级用户拥有所有权限
        if self.is_superuser:
            return True
        return False

    @property
    def is_authenticated(self):
        """
        始终返回True，表示用户已认证
        """
        return True

    @property
    def is_anonymous(self):
        """
        始终返回False，表示用户不是匿名用户
        """
        return False


class UserSession(models.Model):
    """用户会话表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户ID')
    token = models.CharField(max_length=64, verbose_name='JWT令牌ID')
    ip_address = models.CharField(max_length=45, verbose_name='IP地址')
    user_agent = models.TextField(verbose_name='用户代理')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    expires_at = models.DateTimeField(verbose_name='过期时间')
    is_active = models.BooleanField(default=True, verbose_name='是否活跃')

    class Meta:
        db_table = 'user_session'
        verbose_name = '用户会话'
        verbose_name_plural = '用户会话'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['token']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"Session {self.id} - User {self.user_id}"


class CreditRecord(models.Model):
    """信誉分记录表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户ID')
    delta = models.IntegerField(verbose_name='变动分值')
    reason = models.CharField(max_length=255, verbose_name='变动原因')
    score_after = models.IntegerField(verbose_name='变动后分数')
    operator_id = models.BigIntegerField(null=True, blank=True, verbose_name='操作员ID（管理员操作）')
    operator_type = models.CharField(max_length=10, default='system', verbose_name='操作员类型(system/admin)')
    related_entity = models.CharField(max_length=20, null=True, blank=True, verbose_name='相关实体(reservation/check_in)')
    related_id = models.BigIntegerField(null=True, blank=True, verbose_name='相关实体ID')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')

    class Meta:
        db_table = 'credit_record'
        verbose_name = '信誉分记录'
        verbose_name_plural = '信誉分记录'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Credit Record {self.id} - User {self.user_id} ({self.delta})"


class PasswordResetToken(models.Model):
    """密码重置令牌表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户ID')
    token = models.CharField(max_length=64, unique=True, verbose_name='重置令牌')
    email = models.EmailField(verbose_name='重置邮箱')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    expires_at = models.DateTimeField(verbose_name='过期时间')
    is_used = models.BooleanField(default=False, verbose_name='是否已使用')
    used_at = models.DateTimeField(null=True, blank=True, verbose_name='使用时间')

    class Meta:
        db_table = 'password_reset_token'
        verbose_name = '密码重置令牌'
        verbose_name_plural = '密码重置令牌'
        indexes = [
            models.Index(fields=['token']),
            models.Index(fields=['user']),
            models.Index(fields=['expires_at']),
            models.Index(fields=['is_used']),
        ]

    def __str__(self):
        return f"Reset Token {self.id} - User {self.user_id}"

    def is_expired(self):
        """检查令牌是否过期"""
        return timezone.now() > self.expires_at

    def mark_as_used(self):
        """标记令牌为已使用"""
        self.is_used = True
        self.used_at = timezone.now()
        self.save()

    @classmethod
    def create_token(cls, user, email):
        """创建新的重置令牌"""
        # 生成随机令牌
        token = secrets.token_hex(32)

        # 设置过期时间（1小时后）
        expires_at = timezone.now() + timezone.timedelta(hours=1)

        # 创建令牌记录
        reset_token = cls.objects.create(
            user=user,
            token=token,
            email=email,
            expires_at=expires_at
        )

        return reset_token

    @classmethod
    def cleanup_expired_tokens(cls):
        """清理过期的令牌"""
        expired_tokens = cls.objects.filter(
            expires_at__lt=timezone.now(),
            is_used=False
        )
        count = expired_tokens.count()
        expired_tokens.delete()
        return count


class SystemConfig(models.Model):
    """系统配置表"""
    id = models.BigAutoField(primary_key=True, verbose_name='主键ID')
    key = models.CharField(max_length=100, unique=True, verbose_name='配置键')
    value = models.TextField(verbose_name='配置值')
    description = models.CharField(max_length=255, null=True, blank=True, verbose_name='配置描述')
    is_encrypted = models.BooleanField(default=False, verbose_name='是否加密存储')
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'system_config'
        verbose_name = '系统配置'
        verbose_name_plural = '系统配置'
        indexes = [
            models.Index(fields=['key']),
        ]

    def __str__(self):
        return f"Config {self.key}"

    @classmethod
    def get_sm4_key(cls):
        """获取系统SM4密钥"""
        try:
            config = cls.objects.get(key='sm4_system_key')
            return config.value
        except cls.DoesNotExist:
            # 如果不存在，创建新的密钥
            key = SM4Crypto.generate_key()
            cls.objects.create(
                key='sm4_system_key',
                value=key,
                description='系统SM4加密密钥',
                is_encrypted=False
            )
            return key

    @classmethod
    def set_config(cls, key, value, description=None, is_encrypted=False):
        """设置配置项"""
        config, created = cls.objects.get_or_create(
            key=key,
            defaults={
                'value': value,
                'description': description,
                'is_encrypted': is_encrypted
            }
        )
        if not created:
            config.value = value
            config.description = description
            config.is_encrypted = is_encrypted
            config.save()
        return config

    @classmethod
    def get_config(cls, key, default=None):
        """获取配置项"""
        try:
            config = cls.objects.get(key=key)
            return config.value
        except cls.DoesNotExist:
            return default
